variables:
  BASE_PATH: /
  DEPLOY_PATH: ${CI_PROJECT_PATH}
  USER: gxxj
  STAGE_SERVERS: ***********
  PROD_SERVERS: ************* *************
  DOCKER_IMAGE: 'node:22-alpine'

cache:
  key:
    files:
      - pnpm-lock.yaml
  paths:
    - .pnpm-store/

stages:
  - build
  - deploy

.build_template: &build_template
  stage: build
  image: ${DOCKER_IMAGE}
  before_script:
    - corepack enable
    - pnpm config set store-dir .pnpm-store
    - echo "Node $(node -v)"
    - echo "PNPM $(pnpm -v)"
    - export SKIP_INSTALL_SIMPLE_GIT_HOOKS=1
    - pnpm i --frozen-lockfile
  artifacts:
    expire_in: 1 day
    paths:
      - dist

build:staging:
  <<: *build_template
  tags:
    - dev-docker_node:lts-alpine
  only:
    - main
  script:
    - pnpm run build:staging

build:production:
  <<: *build_template
  tags:
    - gov-docker_node:lts-alpine
  only:
    - release
  script:
    - pnpm run build:production

deploy:staging:
  stage: deploy
  tags:
    - dev-frontend-03
  only:
    - main
  script:
    - |
      for server in $STAGE_SERVERS; do
        ssh "${USER}@${server}" "mkdir -p ${DEPLOY_PATH}/${BASE_PATH}"
        rsync -avzq --delete "dist/" "${USER}@${server}:${DEPLOY_PATH}/${BASE_PATH}"
      done

deploy:production:
  stage: deploy
  tags:
    - gov-frontend-03
  only:
    - release
  script:
    - |
      for server in $PROD_SERVERS; do
        ssh "${USER}@${server}" "mkdir -p ${DEPLOY_PATH}/${BASE_PATH}"
        rsync -avzq --delete "dist/" "${USER}@${server}:${DEPLOY_PATH}/${BASE_PATH}"
      done
