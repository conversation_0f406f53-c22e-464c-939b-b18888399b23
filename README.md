# 潍坊高新区电子证照核验系统

## 项目简介

扫码核验电子证书，展示证书信息。

## 技术栈

- 前端框架：Vue3
- 状态管理：Pinia
- UI 组件库：Vant
- 构建工具：Vite
- 包管理：pnpm

## 项目结构

```
cert-verify-app/
├── public/             # 静态资源
├── src/                # 源代码
│   ├── assets/         # 静态资源
│   ├── components/     # 公共组件
│   ├── composables/    # 组合式函数
│   ├── stores/         # 状态管理
│   ├── router/         # 路由配置
│   ├── types/          # 类型定义
│   ├── views/          # 页面组件
│   ├── App.vue         # 根组件
│   └── main.ts         # 入口文件
├── .env                # 环境变量
├── index.html          # HTML模板
├── package.json        # 项目配置
└── README.md           # 项目说明
```
