import antfu from '@antfu/eslint-config'
import autoImport from './.eslintrc-auto-import.json' with { type: 'json' }

export default antfu(
  {
    formatters: true,
    unocss: true,
    vue: true,
    ignores: ['src/assets/**/*', 'eslint.config.js'],
    unicorn: true,
  },
  {
    languageOptions: {
      globals: autoImport.globals,
    },
  },
  {
    rules: {
      'no-console': 'off',
      'no-undef': 'off',
      'no-unused-vars': 'off',
      'no-restricted-syntax': 'off',
      'n/prefer-global/process': 'off',
      'no-irregular-whitespace': 'off',
      'unused-imports/no-unused-vars': 'off',
      'ts/no-invalid-this': 'off',
      'vue/no-unused-vars': 'warn',
      'vue/component-name-in-template-casing': ['error', 'PascalCase', { registeredComponentsOnly: false, ignores: ['template'] }],
      'unicorn/no-null': 'off',
      'unicorn/prevent-abbreviations': 'off',
      'unicorn/prefer-top-level-await': 'off',
    },
  },
  {
    files: ['src/views/**/*.vue'],
    rules: {
      'unicorn/filename-case': ['error', { case: 'kebabCase', ignore: ['App.vue'] }],
    },
  },
  {
    files: ['src/components/**/*.vue', 'src/views/**/components/**/*.vue'],
    rules: {
      'unicorn/filename-case': ['error', { cases: { pascalCase: true, kebabCase: false, }, },],
    },
  },
)
