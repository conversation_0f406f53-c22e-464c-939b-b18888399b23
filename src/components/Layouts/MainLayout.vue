<script setup lang="ts">
import type { RouteLocationNormalizedLoaded } from 'vue-router'

/**
 * 定义标签页项目的结构
 */
interface TabItem {
  // 标签页对应的路径
  path: string
  // 标签页显示的图标
  icon: string
  // 标签页显示的文本
  text: string
}

const active: Ref<number> = ref(0)
const route: RouteLocationNormalizedLoaded = useRoute()
const tabs: Ref<TabItem[]> = ref([{ path: '/', icon: 'home-o', text: '首页' }])
const routes: string[] = tabs.value.map(tab => tab.path)

watch(
  () => route.path,
  (newPath: string) => {
    const index: number = routes.findIndex(path => newPath.startsWith(path))
    if (index > -1) {
      active.value = index
    }
  },
  { immediate: true },
)
</script>

<template>
  <RouterView />
  <VanTabbar v-if="$route.meta?.showTabbar" v-model="active">
    <VanTabbarItem
      v-for="(tab, index) in tabs"
      :key="index"
      :replace="true"
      :to="tab.path"
      :icon="tab.icon"
    >
      {{ tab.text }}
    </VanTabbarItem>
  </VanTabbar>
</template>
