<script setup lang="ts">
import router from '~/router'

/**
 * 自定义路由元信息结构
 */
export interface CustomRouteMeta {
  // 页面标题
  title?: string
  // 是否显示底部标签栏
  showTabbar?: boolean
  // 是否需要认证
  requiresAuth?: boolean
}

const route = useRoute()

const currentRouteMeta = computed(() => route.meta as CustomRouteMeta)

function onClickLeft(): void {
  router.back()
}
</script>

<template>
  <VanNavBar
    v-if="currentRouteMeta.title" :title="currentRouteMeta.title" left-text="返回" left-arrow fixed
    @click-left="onClickLeft"
  />
  <main :class="{ 'pt-46px': currentRouteMeta.title, 'pb-50px': currentRouteMeta.showTabbar }">
    <RouterView />
  </main>
</template>
