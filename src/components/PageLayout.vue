<script setup lang="ts">
interface Props {
  title: string
  showBackButton?: boolean
  backButtonAction?: () => void
  contentBgClass?: string
  contentClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  showBackButton: false,
  backButtonAction: undefined,
  contentBgClass: 'bg-gray-50',
  contentClass: 'flex-1 overflow-y-auto'
})

const router = useRouter()

// 处理返回按钮点击
const handleBackClick = () => {
  if (props.backButtonAction) {
    props.backButtonAction()
  } else {
    router.back()
  }
}
</script>

<template>
  <div class="mx-auto h-screen max-w-500px min-w-320px flex flex-col overflow-hidden text-gray-700">
    <!-- 头部区域 -->
    <header
      class="relative flex-shrink-0 from-blue-600 via-blue-500 to-blue-600 bg-gradient-to-b py-3 text-white shadow-md"
    >
      <!-- 返回按钮 -->
      <div v-if="showBackButton" class="absolute inset-y-0 left-0 flex items-center pl-3">
        <button
          class="h-8 w-8 flex items-center justify-center rounded-full hover:bg-white/20"
          @click="handleBackClick"
        >
          <i class="i-mingcute:arrow-left-line text-xl" />
        </button>
      </div>
      <!-- 标题 -->
      <h1 class="text-center text-xl">
        {{ title }}
      </h1>
    </header>

    <!-- 内容区域 -->
    <div :class="[contentClass, contentBgClass]">
      <slot />
    </div>
  </div>
</template>
