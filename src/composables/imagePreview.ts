import { showImagePreview } from 'vant'

// 整页图片预览
export function useImagePreview() {
  const pageRef: Ref<ComponentPublicInstance | HTMLElement | null> = ref(null)

  function handleImageClick(event: MouseEvent) {
    const target = event.target as HTMLImageElement
    if (!pageRef.value || !target.src)
      return

    // 获取容器元素
    const containerElement = (pageRef.value as ComponentPublicInstance)?.$el ?? pageRef.value
    if (!containerElement || typeof containerElement.querySelectorAll !== 'function')
      return

    // 遍历获取全部图片路径
    const allImages = Array.from(containerElement.querySelectorAll('img')) as HTMLImageElement[]
    const imageUrls = allImages.map(img => new URL(img.src).pathname)
    const clickedSrcPath = new URL(target.src).pathname
    const startPosition = imageUrls.findIndex(url => url === clickedSrcPath)

    if (startPosition === -1)
      return

    showImagePreview({
      images: imageUrls, // 全部图片的数组
      startPosition,
      closeable: true,
    })
  }

  return {
    pageRef,
    handleImageClick,
  }
}
