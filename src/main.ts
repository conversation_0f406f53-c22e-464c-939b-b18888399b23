import { createPinia } from 'pinia'
import { createApp } from 'vue'

import App from './App.vue'
import router from './router'

import { useUmami } from './utils/umami'
import 'virtual:uno.css'
import '@unocss/reset/tailwind.css'
import 'vant/lib/index.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 获取指纹并初始化埋点统计功能
async function initAnalytics() {
  const visitorId = await useFingerprint()
  useUmami(visitorId)
}
initAnalytics()

app.mount('#app')
