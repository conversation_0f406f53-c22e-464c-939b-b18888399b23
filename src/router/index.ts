import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Index',
    component: () => import('~/views/index.vue'),
    meta: { showTabbar: false },
  },
  {
    path: '/:code',
    name: 'Code',
    component: () => import('~/views/code.vue'),
    meta: { showTabbar: false },
  },
  {
    path: '/verify',
    name: 'Verify',
    component: () => import('~/views/verify/index.vue'),
    meta: { showTabbar: false },
  },
  {
    path: '/guide',
    name: 'Guide',
    component: () => import('~/views/guide/index.vue'),
    meta: {
      title: '操作指南',
      showTabbar: false,
    },
  },
  {
    path: '/guide/receive',
    name: 'GuideReceive',
    component: () => import('~/views/guide/receive.vue'),
    meta: {
      title: '领取说明',
      showTabbar: false,
    },
  },
  {
    path: '/guide/verify',
    name: 'GuideVerify',
    component: () => import('~/views/guide/verify.vue'),
    meta: {
      title: '验签说明',
      showTabbar: false,
    },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes,
})

export default router
