/**
 * 格式化接口返回数据并赋值给 certData 响应式数据
 * 当资源路径以 /profile/ 开头时，拼接 API_BASE_URL，省去在 Nginx 配置，多个项目配置容易冲突。
 * @param target 目标对象
 * @param source 源对象
 */
export function useProcessData<T extends Record<string, any>>(target: T, source: T): void {
  const baseUrl = import.meta.env.VITE_API_BASE_URL as string

  const process = (value: any): any => {
    if (typeof value === 'string' && value.startsWith('/profile/')) {
      return baseUrl + value
    }
    if (Array.isArray(value)) {
      return value.map(process)
    }
    if (value && typeof value === 'object') {
      return Object.fromEntries(Object.entries(value).map(([k, v]) => [k, process(v)]))
    }
    return value
  }

  const processed = Object.fromEntries(Object.entries(source).map(([k, v]) => [k, process(v)])) as T
  Object.assign(target, processed)
}
