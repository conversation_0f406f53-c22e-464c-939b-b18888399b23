import type { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'

// 定义响应数据结构
interface ApiResponse<T = any> {
  code: number
  msg?: string
  data?: T
}

export const useRequest: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 3000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
useRequest.interceptors.request.use(
  async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
    config.headers['X-Visitor-Id'] = await useLocalStorage.getItem('visitorId')
    return config
  },
  (error: any): Promise<any> => {
    return Promise.reject(error)
  },
)

// 响应拦截器
useRequest.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>): AxiosResponse<ApiResponse> | Promise<any> => {
    const resData: ApiResponse = response.data
    if (resData?.code !== 200) {
      console.error('响应错误', resData)
      return Promise.reject(resData)
    }
    // 若 response.data = { msg, code, data } 格式返回 response.data.data，若不是直接返回 response.data
    return resData.data ?? resData
  },
  (error: any): Promise<any> => {
    console.error('响应错误', error)
    return Promise.reject(error)
  },
)
