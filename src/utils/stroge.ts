import type { Storage } from 'unstorage'
import Cookies from 'js-cookie'
import { createStorage } from 'unstorage'
import localStorageDriver from 'unstorage/drivers/localstorage'

const prefix: string = `${import.meta.env.VITE_SLUG}<${import.meta.env.VITE_ENV}>`

// 创建 localStorage 存储实例，统一添加应用前缀
export const useLocalStorage: Storage = createStorage({
  driver: localStorageDriver({
    base: prefix,
    storage: window.localStorage,
  }),
})

// 创建 sessionStorage 存储实例，统一添加应用前缀
export const useSessionStorage: Storage = createStorage({
  driver: localStorageDriver({
    base: prefix,
    storage: window.sessionStorage,
  }),
})

// 封装 js-cookie，统一添加应用路径
export const useCookies = {
  get: (name: string | undefined) =>
    name ? Cookies.get(name) : Cookies.get(),
  set: (name: string, value: string, options?: any) =>
    Cookies.set(name, value, {
      expires: 1, // 默认过期时间为 1 天
      path: import.meta.env.VITE_BASE_URL,
      ...options,
    }),
  remove: (name: string, options?: any) =>
    Cookies.remove(name, {
      path: import.meta.env.VITE_BASE_URL,
      ...options,
    }),
}
