export function useUmami(tag: string) {
  const websiteId = import.meta.env.VITE_UMAMI_WEBSITE_ID

  // 如果未设置 VITE_UMAMI_WEBSITE_ID，则不加载 Umami 脚本，当前只在 production 环境配置了
  if (!websiteId)
    return

  const EXISTING_SCRIPT_ID = 'umami-analytics-script'
  if (document.querySelector(`#${EXISTING_SCRIPT_ID}`))
    return

  const script = document.createElement('script')
  script.id = EXISTING_SCRIPT_ID
  script.defer = true
  script.src = 'https://support.wfgxic.com/script.js'
  script.dataset.websiteId = websiteId
  script.dataset.autoTrack = 'true' // 自动跟踪
  if (tag) {
    script.dataset.tag = tag
  }
  document.head.append(script)
}
