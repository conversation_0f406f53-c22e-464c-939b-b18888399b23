<!-- 此页面作为获取 code 的页面，当用户扫码后，会跳转到此页面，然后跳转到 Verify 页面 -->
<script setup lang="ts">
const router = useRouter()
const code: string | string[] = router.currentRoute.value.params.code

onMounted(() => {
  if (code) {
    useSessionStorage.setItem('code', code as string) // 将 code 保存到 session 存储中
    router.replace({ name: 'Verify' })
  }
  else {
    router.replace({ name: 'Index' })
  }
})
</script>

<template>
  <div />
</template>
