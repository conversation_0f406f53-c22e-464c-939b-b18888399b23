<script setup lang="ts">
const router = useRouter()

const guideItems = [
  {
    title: '电子证照验签说明',
    description: '如何验证 OFD 格式电子证照的真伪',
    icon: 'i-mingcute:seal-line',
    route: { name: 'GuideVerify' },
    colors: {
      bg: 'bg-blue-50',
      hoverBg: 'hover:bg-blue-100',
      iconBg: 'bg-blue-100',
      iconText: 'text-blue-500',
      titleText: 'text-blue-800',
      descriptionText: 'text-blue-600',
    },
  },
  {
    title: '电子证照领取说明',
    description: '如何在爱山东领取电子证照',
    icon: 'i-mingcute:document-line',
    route: { name: 'GuideReceive' },
    colors: {
      bg: 'bg-green-50',
      hoverBg: 'hover:bg-green-100',
      iconBg: 'bg-green-100',
      iconText: 'text-green-500',
      titleText: 'text-green-800',
      descriptionText: 'text-green-600',
    },
  },
]
</script>

<template>
  <GuideLayout title="操作指南">
    <div class="p-4">
      <div class="space-y-4">
        <div
          v-for="item in guideItems"
          :key="item.title"
          class="flex cursor-pointer items-center rounded-xl p-4 transition-all hover:shadow-md"
          :class="[item.colors.bg, item.colors.hoverBg]"
          @click="router.push(item.route)"
        >
          <div
            class="mr-4 h-12 w-12 flex flex-shrink-0 items-center justify-center rounded-full"
            :class="item.colors.iconBg"
          >
            <i class="text-3xl" :class="[item.icon, item.colors.iconText]" />
          </div>
          <div>
            <h3 class="text-lg font-semibold" :class="item.colors.titleText">
              {{ item.title }}
            </h3>
            <p class="text-sm" :class="item.colors.descriptionText">
              {{ item.description }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </GuideLayout>
</template>
