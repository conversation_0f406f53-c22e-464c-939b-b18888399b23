<script setup lang="ts">
import PageLayout from '@/components/PageLayout.vue'

const { pageRef } = useImagePreview()

const steps: GuideStep[] = [
  {
    title: '进入【我的】页面',
    description: '打开“爱山东”政务 APP，点击【我的】。',
    images: [
      { src: '/images/guide/receive/1.png' },
    ],
  },
  {
    title: '查看全部证照',
    description: '在【我的】页面中，点击中间部分的【我的证照】右侧的【查看全部】按钮。',
    images: [
      { src: '/images/guide/receive/2.png' },
    ],
  },
  {
    title: '进行刷脸认证',
    description: '按照要求【刷脸认证】，点击【立即刷脸】，进入【电子卡包】页面。',
    images: [
      { src: '/images/guide/receive/3.png' },
    ],
  },
  {
    title: '申领毕业证',
    description: '在电子卡包页面中，点击【证照申领】，在证照申领页面的顶部输入框中，输入【毕业证】，然后点击搜索按钮，在出现的列表中点击对应的毕业证的后方的按钮，即可领取成功。',
    images: [
      { src: '/images/guide/receive/4.png' },
      { src: '/images/guide/receive/5.png' },
    ],
  },
  {
    title: '查看领取的证照',
    description: '提示"申领成功"后，返回到【我的卡包】页面，点击【我的证照】，即可查看到领取的电子毕业证。',
    images: [
      { src: '/images/guide/receive/6.png' },
    ],
  },
]
</script>

<template>
  <PageLayout ref="pageRef" title="电子证照领取说明" :show-back-button="true">
    <GuideSteps title="爱山东领取电子证照操作步骤" :steps="steps" />
  </PageLayout>
</template>
