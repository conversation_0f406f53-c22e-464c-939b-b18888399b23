<script setup lang="ts">
import PageLayout from '@/components/PageLayout.vue'

const { pageRef } = useImagePreview()

const steps: GuideStep[] = [
  {
    title: '下载电子证照',
    description: '下载 OFD 格式的电子证照。',
    images: [{ src: '/images/guide/verify/1.png' }],
  },
  {
    title: '电脑端验签',
    description: '用 WPS 打开下载的电子证照，在需要验签的电子印章上面右击。',
    images: [
      { src: '/images/guide/verify/2.png' },
      { src: '/images/guide/verify/3.png', caption: '查看验章，返回【该签章有效】则表示签章是有效的未被修改。' },
      { src: '/images/guide/verify/4.png', caption: '查验详情，返回【该签章有效】则表示签章是有效的未被修改，可以从页面中查看到签章人、签章时间、印章名称等信息。还可以点击【印章属性】查看详细的印章信息。' },
    ],
  },
  {
    title: '手机端验签',
    description: '首先在手机上安装 WPS 软件，然后用 WPS 打开下载的电子证照，此时会弹出提示“该文档的所有签章有效！内容未被修改。”，则表示该证照的内容没有被修改过。（手机端验签方式操作仅供参考，具体操作方式因手机版本不同或有差异。）',
    images: [
      { src: '/images/guide/verify/5.png' },
      { src: '/images/guide/verify/6.png', caption: '也可以通过点击【验章】按钮，进行验章。' },
      { src: '/images/guide/verify/7.png', caption: '验证通过后返回详细的签章信息。' },
    ],
  },
]
</script>

<template>
  <PageLayout ref="pageRef" title="电子证照验签说明" :show-back-button="true">
    <GuideSteps title="OFD 格式电子证照验签步骤" :steps="steps" />
  </PageLayout>
</template>
