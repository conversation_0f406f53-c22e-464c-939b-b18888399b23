<!-- 此页面作为 Verify 的默认页面，当没有 code 时，显示此页面，提示重新扫码 -->
<script setup lang="ts">
const title = computed<string>(() => `${import.meta.env.VITE_TITLE}`)
</script>

<template>
  <div class="mx-auto h-screen max-w-500px min-w-320px flex flex-col overflow-hidden text-gray-700">
    <!-- 头部区域 -->
    <header class="flex-shrink-0 from-blue-600 via-blue-500 to-blue-600 bg-gradient-to-b py-3 text-white shadow-md">
      <h1 class="text-center text-xl">
        {{ title }}
      </h1>
    </header>

    <!-- 状态显示区域（无结果） -->
    <div class="flex flex-1 flex-col items-center justify-center bg-white">
      <div class="relative mb-6 h-24 w-24">
        <div class="absolute inset-0 border-4 border-orange-100 rounded-full" />
        <div class="absolute inset-0 border-4 border-orange-200 rounded-full opacity-50" />
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="i-mingcute:alert-line text-3xl text-orange-500" />
        </div>
      </div>
      <p class="mb-2 text-lg text-orange-700 font-medium">
        请重新扫码查询
      </p>
    </div>
  </div>
</template>
