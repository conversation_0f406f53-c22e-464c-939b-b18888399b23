<script setup lang="ts">
import EducationCert from './components/EducationCert.vue'

const title = computed<string>(() => (import.meta.env.VITE_TITLE as string).replace('平台', '核验结果'))
const router = useRouter()
const code = ref<string>('')
const certData = reactive<Partial<CertData>>({})
const loading = ref<boolean>(true)
const notFound = ref<boolean>(false)
const errorMsg = ref<string>('')

// 获取证书信息
async function getCert(code: string): Promise<void> {
  try {
    loading.value = true
    notFound.value = false
    errorMsg.value = ''

    const res: CertData = await ApiGetCert(code)

    if (!res || Object.keys(res).length === 0) {
      notFound.value = true
      errorMsg.value = '未查询到该证照信息'
      return
    }

    useProcessData(certData, res)
  }

  catch (error: any) {
    notFound.value = true
    errorMsg.value = '未查询到该证照信息'
  }
  finally {
    loading.value = false
  }
}

// 根据证书类型获取对应组件
const certComponent = computed<Component>(() => {
  const componentMap: Record<string, Component> = {
    [CertType.EDUCATION_PRIMARY]: EducationCert, // 小学毕业证
    [CertType.EDUCATION_MIDDLE]: EducationCert, // 初中毕业证
  }
  return componentMap[certData.type ?? CertType.EDUCATION_PRIMARY] || EducationCert
})

onMounted(async () => {
  code.value = await useSessionStorage.getItem('code') as string // 从 session 存储中获取 code
  if (!code.value) {
    router.replace({ name: 'Index' })
    return
  }
  getCert(code.value)
})
</script>

<template>
  <div class="relative mx-auto h-screen max-w-500px min-w-320px flex flex-col overflow-hidden text-gray-700">
    <!-- 头部区域 -->
    <header class="flex-shrink-0 from-blue-600 via-blue-500 to-blue-600 bg-gradient-to-b py-3 text-white shadow-md">
      <h1 class="text-center text-xl">
        {{ certData.title || title }}
      </h1>
    </header>

    <!-- 状态显示区域（加载中或未查询到） -->
    <div v-if="loading || notFound" class="flex flex-1 flex-col items-center justify-center bg-white">
      <div class="relative mb-6 h-24 w-24">
        <div
          class="absolute inset-0 border-4 rounded-full"
          :class="loading ? 'border-blue-100' : 'border-orange-100'"
        />
        <div
          class="absolute inset-0 rounded-full"
          :class="loading ? 'animate-spin border-4 border-t-blue-500' : 'border-4 border-orange-200 opacity-50'"
        />
        <div class="absolute inset-0 flex items-center justify-center">
          <div v-if="loading" class="i-mingcute:certificate-line text-3xl text-blue-500" />
          <div v-else class="i-mingcute:alert-line text-3xl text-orange-500" />
        </div>
      </div>
      <p class="mb-2 text-lg font-medium" :class="loading ? 'text-blue-700' : 'text-orange-700'">
        {{ loading ? '正在加载' : '查询失败' }}
      </p>
      <p class="text-gray-500">
        {{ loading ? '请稍候，正在核验证照信息...' : (errorMsg || '请确认证照核验码是否正确') }}
      </p>
    </div>

    <!-- 使用动态组件显示证书模板 -->
    <component :is="certComponent" v-else :cert-data="certData" />
  </div>
</template>
