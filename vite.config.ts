import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { VantResolver } from '@vant/auto-import-resolver'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv } from 'vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const __dirname = path.dirname(fileURLToPath(import.meta.url))

  return {
    base: env.VITE_BASE_URL,

    resolve: {
      alias: {
        '~': path.resolve(__dirname, 'src'),
        '@': path.resolve(__dirname, 'src'),
      },
    },

    plugins: [
      vue(),
      UnoCSS(),
      AutoImport({
        imports: [
          'vue',
          'pinia',
          'vue-router',
        ],
        dts: true,
        dirs: [
          './src/composables',
          './src/utils',
          './src/api',
          './src/types',
        ],
        vueTemplate: true,
        resolvers: [VantResolver()],
        eslintrc: {
          enabled: true,
        },
      }),
      Components({
        resolvers: [VantResolver()],
        dirs: [
          './src/components',
          './src/views/**/components',
        ],
        dts: true,
      }),
    ],

    server: {
      open: false,
      host: '0.0.0.0',
      port: Number(env.DEV_SERVER_PORT) || 5173,
      proxy: {
        '/api': {
          target: env.DEV_SERVER_PROXY_TARGET,
          changeOrigin: true,
        },
        '/profile': {
          target: env.DEV_SERVER_PROXY_TARGET,
          rewrite: path => path.replace(/^\/profile/, '/api/certificate/profile'),
          changeOrigin: true,
        },
      },
    },

    esbuild: {
      pure: ['console.log', 'console.warn', 'console.info', 'console.table', 'console.dir'],
      drop: ['debugger'],
      legalComments: 'none',
    },
  }
})
